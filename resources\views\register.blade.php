@extends('layouts.app')

@section('title', 'Daftar Gratis - Digitalisasi Sekolah dengan Smart School Project')

@section('description',
    'Daftar gratis sekarang dan mulai digitalisasi manajemen sekolah Anda. Nikmati fitur leng<PERSON>,
    k<PERSON><PERSON><PERSON> pen<PERSON>, dan aftersales terbaik dari Smart School Project.')

@section('keywords',
    'daftar smart school, coba gratis, trial smart school, pendaftaran sekolah digital, demo gratis
    sistem sekolah, registrasi smart school')

@section('og_title', 'Daftar Gratis - Smart School Project')

@section('og_description',
    '<PERSON><PERSON> transformasi digital sekolah Anda. Daftar gratis dan dapatkan akses ke platform Smart
    School Project.')

@section('content')
    <!-- Registration Section -->
    <section id="registration" class="registration-section">
        <div class="container">
            <!-- Header -->
            <div class="row">
                <div class="col-lg-10 mx-auto text-center mb-5">
                    <div class="section-badge">
                        <i class="fas fa-rocket me-2"></i>
                        <PERSON><PERSON>
                    </div>
                    <h1 class="registration-title">
                        Digitalisasi Manajemen Sekolah Anda dengan Smart School Project
                    </h1>
                    <p class="registration-subtitle">
                        Nikmati berbagai fitur unggulan Smart School Project dan rasakan bagaimana sistem kami dapat
                        memudahkan manajemen sekolah Anda.
                    </p>
                </div>
            </div>

            <!-- Keunggulan -->
            <div class="row mb-5">
                <div class="col-lg-10 mx-auto">
                    <div class="advantages-grid">
                        <!-- Advantage 1 -->
                        <div class="advantage-card">
                            <div class="advantage-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3 class="advantage-title">Fitur Lengkap</h3>
                            <p class="advantage-description">
                                Sistem terintegrasi dengan fitur-fitur lengkap untuk manajemen sekolah modern, mulai
                                dari
                                administrasi, pembelajaran, hingga komunikasi dengan orang tua.
                            </p>
                        </div>

                        <!-- Advantage 2 -->
                        <div class="advantage-card">
                            <div class="advantage-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <h3 class="advantage-title">Kemudahan</h3>
                            <p class="advantage-description">
                                Interface yang user-friendly dan mudah digunakan oleh semua kalangan, dari admin
                                sekolah,
                                guru, siswa, hingga orang tua tanpa perlu pelatihan yang rumit.
                            </p>
                        </div>

                        <!-- Advantage 3 -->
                        <div class="advantage-card">
                            <div class="advantage-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <h3 class="advantage-title">Aftersales Terbaik</h3>
                            <p class="advantage-description">
                                Tim support kami siap membantu Anda 24/7 dengan respon cepat, update berkala, dan
                                maintenance rutin untuk memastikan sistem berjalan optimal.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="registration-form-wrapper">
                        <div class="form-header">
                            <h2 class="form-title">Mari Bergabung Bersama Smart School Project</h2>
                            <p class="form-subtitle">
                                Masukkan data dengan benar agar kami mudah saat menghubungi anda
                            </p>
                        </div>

                        @if (session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                    aria-label="Close"></button>
                            </div>
                        @endif

                        @if ($errors->any())
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                    aria-label="Close"></button>
                            </div>
                        @endif

                        <form id="registrationForm" class="registration-form" action="{{ route('register.submit') }}"
                            method="POST">
                            @csrf
                            <!-- Nama -->
                            <div class="mb-4">
                                <label for="nama" class="form-label">Nama <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('nama') is-invalid @enderror"
                                    id="nama" name="nama" required value="{{ old('nama') }}"
                                    placeholder="Masukkan nama lengkap Anda">
                                @error('nama')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Jabatan -->
                            <div class="mb-4">
                                <label for="jabatan" class="form-label">Jabatan <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('jabatan') is-invalid @enderror"
                                    id="jabatan" name="jabatan" required value="{{ old('jabatan') }}"
                                    placeholder="Contoh: Kepala Sekolah, Wakil Kepala Sekolah">
                                @error('jabatan')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Alamat Email Perorangan -->
                            <div class="mb-4">
                                <label for="email_perorangan" class="form-label">Alamat Email (Perorangan) <span
                                        class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email_perorangan') is-invalid @enderror"
                                    id="email_perorangan" name="email_perorangan" required
                                    value="{{ old('email_perorangan') }}" placeholder="<EMAIL>">
                                @error('email_perorangan')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Alamat Email Sekolah -->
                            <div class="mb-4">
                                <label for="email_sekolah" class="form-label">Alamat Email Sekolah <span
                                        class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email_sekolah') is-invalid @enderror"
                                    id="email_sekolah" name="email_sekolah" required value="{{ old('email_sekolah') }}"
                                    placeholder="<EMAIL>">
                                @error('email_sekolah')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nomor Telephone (WhatsApp) -->
                            <div class="mb-4">
                                <label for="whatsapp" class="form-label">Nomor Telephone (WhatsApp) <span
                                        class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('whatsapp') is-invalid @enderror"
                                    id="whatsapp" name="whatsapp" required value="{{ old('whatsapp') }}"
                                    placeholder="08xxxxxxxxxx">
                                <small class="form-text text-muted">Pastikan nomor WhatsApp aktif</small>
                                @error('whatsapp')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nama Institusi -->
                            <div class="mb-4">
                                <label for="institusi" class="form-label">Nama Institusi <span
                                        class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('institusi') is-invalid @enderror"
                                    id="institusi" name="institusi" required value="{{ old('institusi') }}"
                                    placeholder="Nama sekolah/institusi pendidikan">
                                @error('institusi')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Pesan Utama (Checklist) -->
                            <div class="mb-4">
                                <label class="form-label">Pesan Utama <span class="text-danger">*</span></label>
                                <div class="form-check">
                                    <input class="form-check-input @error('pesan_utama') is-invalid @enderror"
                                        type="checkbox" id="profil_sekolah" name="pesan_utama[]" value="Profil Sekolah"
                                        {{ in_array('Profil Sekolah', old('pesan_utama', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="profil_sekolah">
                                        Profil Sekolah
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input @error('pesan_utama') is-invalid @enderror"
                                        type="checkbox" id="ppdb_online" name="pesan_utama[]" value="PPDB Online"
                                        {{ in_array('PPDB Online', old('pesan_utama', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="ppdb_online">
                                        PPDB Online
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input @error('pesan_utama') is-invalid @enderror"
                                        type="checkbox" id="buku_tamu" name="pesan_utama[]" value="Buku Tamu Digital"
                                        {{ in_array('Buku Tamu Digital', old('pesan_utama', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="buku_tamu">
                                        Buku Tamu Digital
                                    </label>
                                </div>

                                <!-- Layanan Berbayar (Disabled) -->
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="absensi_sekolah"
                                        name="pesan_utama[]" value="Absensi Sekolah" disabled>
                                    <label class="form-check-label text-muted" for="absensi_sekolah">
                                        Absensi Sekolah <span class="badge bg-warning text-dark ms-2">Berbayar</span>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="absensi_siswa"
                                        name="pesan_utama[]" value="Absensi Siswa" disabled>
                                    <label class="form-check-label text-muted" for="absensi_siswa">
                                        Absensi Siswa <span class="badge bg-warning text-dark ms-2">Berbayar</span>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="spp_online" name="pesan_utama[]"
                                        value="SPP Online" disabled>
                                    <label class="form-check-label text-muted" for="spp_online">
                                        SPP Online <span class="badge bg-warning text-dark ms-2">Berbayar</span>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="bimbingan_konseling"
                                        name="pesan_utama[]" value="Bimbingan Konseling" disabled>
                                    <label class="form-check-label text-muted" for="bimbingan_konseling">
                                        Bimbingan Konseling <span class="badge bg-warning text-dark ms-2">Berbayar</span>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="lms" name="pesan_utama[]"
                                        value="LMS" disabled>
                                    <label class="form-check-label text-muted" for="lms">
                                        LMS <span class="badge bg-warning text-dark ms-2">Berbayar</span>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="cbt" name="pesan_utama[]"
                                        value="CBT" disabled>
                                    <label class="form-check-label text-muted" for="cbt">
                                        CBT <span class="badge bg-warning text-dark ms-2">Berbayar</span>
                                    </label>
                                </div>

                                <small class="form-text text-muted">Pilih minimal satu layanan yang Anda minati</small>
                                @error('pesan_utama')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Pesan Tambahan -->
                            <div class="mb-4">
                                <label for="pesan_tambahan" class="form-label">Pesan Tambahan</label>
                                <textarea class="form-control @error('pesan_tambahan') is-invalid @enderror" id="pesan_tambahan"
                                    name="pesan_tambahan" rows="4" placeholder="Sampaikan kebutuhan atau pertanyaan Anda di sini...">{{ old('pesan_tambahan') }}</textarea>
                                @error('pesan_tambahan')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg submit-btn">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Kirim Pendaftaran
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('additional_css')
    <style>
        /* Styling untuk checklist layanan berbayar */
        .form-check input[type="checkbox"]:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            margin-left: .25rem;
        }

        .form-check input[type="checkbox"]:disabled+label {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .form-check input[type="checkbox"]:disabled+label .badge {
            opacity: 1;
        }

        /* Spacing untuk section layanan berbayar */
        .form-check:has(input:disabled) {
            margin-top: 0.75rem;
            padding: 0.25rem 0;
            border-left: 3px solid #ffc107;
            padding-left: 0.75rem;
            margin-left: 0.5rem;
            background-color: #fffbf0;
            border-radius: 0 5px 5px 0;
        }
    </style>
@endsection
