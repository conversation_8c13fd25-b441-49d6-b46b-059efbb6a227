<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Models\LandingRegistration;
use App\Mail\RegistrationNotification;

class RegistrationController extends Controller
{
    /**
     * Display the registration form.
     */
    public function index()
    {
        return view('register');
    }

    /**
     * Handle the registration form submission.
     */
    public function store(Request $request)
    {
        // Validate the form data
        $validator = Validator::make($request->all(), [
            'nama' => 'required|string|max:255',
            'jabatan' => 'required|string|max:255',
            'email_perorangan' => 'required|email|max:255',
            'email_sekolah' => 'required|email|max:255',
            'whatsapp' => 'required|string|max:20',
            'institusi' => 'required|string|max:255',
            'pesan_utama' => 'required|array|min:1',
            'pesan_utama.*' => 'string|in:Profil <PERSON>,PPDB Online,Buku Tamu Digital',
            'pesan_tambahan' => 'nullable|string|max:1000',
        ], [
            'nama.required' => 'Nama wajib diisi.',
            'jabatan.required' => 'Jabatan wajib diisi.',
            'email_perorangan.required' => 'Email perorangan wajib diisi.',
            'email_perorangan.email' => 'Format email perorangan tidak valid.',
            'email_sekolah.required' => 'Email sekolah wajib diisi.',
            'email_sekolah.email' => 'Format email sekolah tidak valid.',
            'whatsapp.required' => 'Nomor WhatsApp wajib diisi.',
            'institusi.required' => 'Nama institusi wajib diisi.',
            'pesan_utama.required' => 'Pilih minimal satu layanan yang Anda minati.',
            'pesan_utama.min' => 'Pilih minimal satu layanan yang Anda minati.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Store the registration data in database
        $registration = LandingRegistration::create([
            'nama' => $request->nama,
            'jabatan' => $request->jabatan,
            'email_perorangan' => $request->email_perorangan,
            'email_sekolah' => $request->email_sekolah,
            'whatsapp' => $request->whatsapp,
            'institusi' => $request->institusi,
            'pesan_utama' => implode(', ', $request->pesan_utama),
            'pesan_tambahan' => $request->pesan_tambahan,
            'source' => 'landing_page',
            'status' => 'pending',
        ]);

        // Store registration ID in session for success page
        session(['registration_id' => $registration->id]);

        // Send email notification to sales team
        try {
            Mail::to('<EMAIL>')
                ->send(new RegistrationNotification($registration));
        } catch (\Exception $e) {
            // Log the error but don't fail the registration
            Log::error('Failed to send registration notification email: ' . $e->getMessage());
        }

        // Redirect to success page
        return redirect()->route('register.success');
    }

    /**
     * Display the registration success page.
     */
    public function success()
    {
        // Check if there's registration ID in session
        if (!session('registration_id')) {
            return redirect()->route('register');
        }

        return view('registration-success');
    }
}
