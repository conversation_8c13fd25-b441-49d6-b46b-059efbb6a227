<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LandingRegistration extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'landing_registrations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nama',
        'jabatan',
        'email_perorangan',
        'email_sekolah',
        'whatsapp',
        'institusi',
        'pesan_utama',
        'pesan_tambahan',
        'source',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the services as an array.
     *
     * @return array
     */
    public function getServicesAttribute()
    {
        return explode(', ', $this->pesan_utama);
    }

    /**
     * Set the services from an array.
     *
     * @param array $services
     * @return void
     */
    public function setServicesAttribute($services)
    {
        $this->attributes['pesan_utama'] = is_array($services) ? implode(', ', $services) : $services;
    }

    /**
     * Scope for filtering by status.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for recent registrations.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
