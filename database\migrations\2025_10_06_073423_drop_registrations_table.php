<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('registrations');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('registrations', function (Blueprint $table) {
            $table->id();
            $table->string('nama');
            $table->string('jabatan');
            $table->string('email_perorangan');
            $table->string('email_sekolah');
            $table->string('whatsapp', 20);
            $table->string('institusi');
            $table->text('pesan_utama');
            $table->text('pesan_tambahan')->nullable();
            $table->timestamps();
        });
    }
};
