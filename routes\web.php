<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\RegistrationController;
use App\Http\Controllers\ContactController;

// Home page
Route::get('/', [HomeController::class, 'index'])->name('home');

// Registration routes
Route::get('/daftar', [RegistrationController::class, 'index'])->name('register');
Route::post('/daftar', [RegistrationController::class, 'store'])->name('register.submit');
Route::get('/pendaftaran-berhasil', [RegistrationController::class, 'success'])->name('register.success');

// Contact page
Route::get('/hubungi-kami', [ContactController::class, 'index'])->name('contact');

// SEO routes
Route::get('/sitemap.xml', function () {
    $urls = [
        ['url' => route('home'), 'lastmod' => now()->toDateString(), 'priority' => '1.0'],
        ['url' => route('register'), 'lastmod' => now()->toDateString(), 'priority' => '0.8'],
        ['url' => route('contact'), 'lastmod' => now()->toDateString(), 'priority' => '0.8'],
    ];

    return response()->view('sitemap', compact('urls'))
        ->header('Content-Type', 'application/xml');
})->name('sitemap');

Route::get('/robots.txt', function () {
    $content = "User-agent: *\n";
    $content .= "Allow: /\n";
    $content .= "Sitemap: " . route('sitemap') . "\n";

    return response($content)->header('Content-Type', 'text/plain');
})->name('robots');
