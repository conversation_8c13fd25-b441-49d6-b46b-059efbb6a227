/* Custom CSS for SIDIGS Navbar */

/* Root Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --accent-color: #ff6b35;
    --light-gray: #f8f9fa;
    --dark-gray: #343a40;
    --white: #ffffff;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding-top: 80px;
    /* Account for fixed navbar */
}

/* Navbar Styles */
.navbar {
    background-color: var(--white) !important;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    box-shadow: var(--shadow);
    padding: 0.5rem 0;
}

/* Brand/Logo Styles */
.navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--dark-gray) !important;
    font-weight: 600;
}

.navbar-brand:hover {
    color: var(--primary-color) !important;
}

.navbar-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 12px;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.brand-content {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.brand-text {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    line-height: 1.1;
    text-transform: uppercase;
}

.brand-subtitle {
    font-size: 11px;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
    margin-top: 2px;
    line-height: 1;
}

/* Navigation Links */
.navbar-nav {
    gap: 1rem;
}

.nav-link {
    color: var(--dark-gray) !important;
    font-weight: 500;
    font-size: 15px;
    padding: 0.5rem 1rem !important;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(0, 123, 255, 0.1);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background-color: rgba(0, 123, 255, 0.1);
}

.nav-link.spkib-link.active {
    color: white !important;
}

/* Special styling for Profil Sekolah link */
.spkib-link {
    background: linear-gradient(135deg, var(--accent-color), #ff4500);
    color: white !important;
    font-weight: 600;
    border-radius: 20px;
    padding: 0.5rem 1.2rem !important;
}

.spkib-link:hover {
    background: linear-gradient(135deg, #ff4500, var(--accent-color));
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

/* Action Buttons */
.navbar-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    padding: 0.5rem 1.2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
    font-weight: 500;
    padding: 0.5rem 1.2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .navbar-brand {
        align-items: center;
    }

    .brand-text {
        font-size: 18px;
    }

    .brand-subtitle {
        font-size: 10px;
    }

    .navbar-nav {
        margin-top: 1rem;
        gap: 0.5rem;
    }

    .navbar-actions {
        margin-top: 1rem;
        flex-direction: column;
        width: 100%;
    }

    .navbar-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    /* Hero Section Mobile */
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-buttons {
        text-align: center;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }

    .hero-image {
        margin-top: 2rem;
        order: -1;
    }

    .floating-card {
        display: none;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    /* About Section Mobile */
    .about-title {
        font-size: 2rem;
    }

    .about-description {
        font-size: 1rem;
    }

    .about-cta .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        text-align: center;
    }

    .about-cta .btn.ms-3 {
        margin-left: 0 !important;
    }

    .image-decoration {
        display: none;
    }

    .about-image {
        margin-bottom: 2rem;
        order: -1;
    }

    .about-content {
        padding-left: 0;
    }

    .about-features .col-md-6 {
        width: 100%;
        margin-bottom: 1rem;
    }

    .feature-item {
        padding: 1.5rem 1rem;
    }

    /* Why Choose Section Mobile */
    .section-title {
        font-size: 2rem;
    }

    .section-description {
        font-size: 1rem;
    }

    .card-image {
        height: 150px;
    }

    .card-content {
        padding: 1rem;
    }

    .card-content h5 {
        font-size: 1rem;
    }

    .card-content p {
        font-size: 0.85rem;
    }

    /* Features Section Mobile */
    .col-lg-2-4 {
        width: 50%;
    }

    .feature-image {
        height: 100px;
    }

    .feature-content {
        padding: 0.75rem;
    }

    .feature-content h6 {
        font-size: 0.85rem;
    }

    .feature-content p {
        font-size: 0.75rem;
    }
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .col-lg-2-4 {
        width: 50%;
    }
}

/* Mobile Responsive */
@media (max-width: 576px) {
    .col-lg-2-4 {
        width: 100%;
    }

    /* Partners Section Mobile */
    .partner-item {
        height: 80px;
        padding: 1rem;
    }

    .partner-logo {
        max-height: 40px;
        max-width: 100px;
    }

    .trust-statement {
        font-size: 1rem;
        padding: 1rem;
    }

    /* Services Section Mobile */
    .service-image {
        height: 160px;
    }

    .service-content {
        padding: 1.5rem;
    }

    .service-content h4 {
        font-size: 1.2rem;
    }

    .service-content p {
        font-size: 0.9rem;
    }

    .feature-tag {
        font-size: 0.75rem;
        padding: 0.3rem 0.6rem;
    }

    /* Promo Section Mobile */
    .promo-image {
        height: 180px;
    }

    .promo-content {
        padding: 1.5rem;
    }

    .promo-content h4 {
        font-size: 1.3rem;
    }

    .promo-description {
        font-size: 0.9rem;
    }

    .promo-features .feature-item {
        margin-bottom: 0.25rem;
    }

    .promo-features .feature-item i {
        font-size: 0.75rem;
        margin-right: 0.3rem;
    }

    .promo-features .feature-item span {
        font-size: 0.8rem;
    }

    .promo-price {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .free-price {
        font-size: 1.1rem;
    }

    .promo-cta {
        padding: 2rem 1.5rem;
    }

    .promo-cta h3 {
        font-size: 1.5rem;
    }

    .promo-cta p {
        font-size: 1rem;
    }

    /* Testimonials Section Mobile */
    .testimonial-card {
        padding: 2rem 1.5rem;
    }

    .testimonial-content {
        padding-top: 1.5rem;
    }

    .testimonial-text {
        font-size: 1rem;
        margin: 1.5rem 0;
    }

    .testimonial-author {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .author-image {
        width: 60px;
        height: 60px;
    }

    .author-info h5 {
        font-size: 1rem;
    }

    .author-info span {
        font-size: 0.85rem;
    }

    .carousel-control-prev {
        left: -40px;
    }

    .carousel-control-next {
        right: -40px;
    }

    .carousel-control-icon {
        width: 50px;
        height: 50px;
    }

    .carousel-control-icon i {
        font-size: 1rem;
    }

    .quote-icon {
        width: 50px;
        height: 50px;
        top: -12px;
    }

    .quote-icon i {
        font-size: 1.2rem;
    }

    /* CTA Section Mobile */
    .cta-section {
        padding: 4rem 0;
    }

    .cta-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .cta-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .cta-features {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .cta-feature {
        justify-content: center;
    }

    .cta-actions {
        margin-top: 2rem;
    }

    .cta-btn-primary,
    .cta-btn-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .cta-contact {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
    }

    .contact-item {
        font-size: 0.85rem;
    }

    /* Footer Section Mobile */
    .footer-section {
        padding: 3rem 0 0;
    }

    .footer-widget {
        margin-bottom: 2.5rem;
        text-align: center;
    }

    .footer-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-social {
        justify-content: center;
    }

    .footer-contact {
        text-align: left;
    }

    .footer-contact .contact-item {
        justify-content: flex-start;
        text-align: left;
    }

    .footer-bottom {
        text-align: center;
    }

    .footer-legal {
        justify-content: center;
        margin-top: 1rem;
        gap: 1rem;
    }

    /* Contact Steps Mobile */
    .contact-steps-section {
        padding: 6rem 0 3rem;
    }

    .steps-timeline::before {
        left: 20px;
    }

    .step-item {
        padding-left: 70px;
        margin-bottom: 3rem;
    }

    .step-number {
        width: 50px;
        height: 50px;
    }

    .step-number span {
        font-size: 1.2rem;
    }

    .step-content {
        padding: 1.5rem;
    }

    .step-icon {
        width: 60px;
        height: 60px;
    }

    .step-icon i {
        font-size: 1.5rem;
    }

    .step-title {
        font-size: 1.2rem;
    }

    .step-description {
        font-size: 0.95rem;
    }

    .contact-cta {
        padding: 2rem 1.5rem;
    }

    .contact-cta h3 {
        font-size: 1.5rem;
    }

    .contact-cta p {
        font-size: 1rem;
    }

    /* Registration Section Mobile */
    .registration-section {
        padding: 6rem 0 3rem;
    }

    .registration-title {
        font-size: 1.8rem;
    }

    .registration-subtitle {
        font-size: 1rem;
    }

    .advantages-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .advantage-card {
        padding: 1.5rem;
    }

    .advantage-icon {
        width: 70px;
        height: 70px;
    }

    .advantage-icon i {
        font-size: 1.8rem;
    }

    .advantage-title {
        font-size: 1.2rem;
    }

    .registration-form-wrapper {
        padding: 2rem 1.5rem;
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-subtitle {
        font-size: 0.9rem;
    }

    .submit-btn {
        width: 100%;
        min-width: auto;
    }

    /* Success Section Mobile */
    .success-section {
        padding: 6rem 0 3rem;
    }

    .success-wrapper {
        padding: 2.5rem 1.5rem;
    }

    .success-icon-wrapper {
        width: 120px;
        height: 120px;
    }

    .success-icon {
        width: 120px;
        height: 120px;
    }

    .success-icon i {
        font-size: 4rem;
    }

    .circle-1,
    .circle-2,
    .circle-3 {
        width: 120px;
        height: 120px;
    }

    .success-title {
        font-size: 2rem;
    }

    .success-message {
        font-size: 1rem;
    }

    .next-steps-title {
        font-size: 1.3rem;
    }

    .step-item-success {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .step-icon-success {
        margin: 0 auto;
    }

    .success-actions {
        flex-direction: column;
    }

    .success-actions .btn {
        width: 100%;
    }

    .contact-text {
        font-size: 0.85rem;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow: hidden;
}

.hero-content {
    padding: 2rem 0;
    position: relative;
    z-index: 5;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--secondary-color);
    margin-bottom: 2rem;
}

.hero-buttons {
    margin-bottom: 3rem;
}

.hero-buttons .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

/* Hero Stats */
.hero-stats {
    padding: 1.5rem 0;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Hero Image */
.hero-image {
    position: relative;
    padding: 2rem 0;
    z-index: 5;
}

.image-container {
    position: relative;
    border-radius: 20px;
    overflow: visible;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.hero-main-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
    transition: transform 0.3s ease;
}

.hero-main-image:hover {
    transform: scale(1.02);
}

/* Floating Cards */
.floating-card {
    position: absolute;
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 3s ease-in-out infinite;
    z-index: 10;
    min-width: 180px;
}

.card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.card-content i {
    font-size: 1.5rem;
}

.card-content h6 {
    margin: 0;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--dark-gray);
}

.card-content p {
    margin: 0;
    font-size: 0.75rem;
    color: var(--secondary-color);
}

.card-1 {
    top: -20%;
    right: 15%;
    animation-delay: 0s;
}

.card-2 {
    bottom: 15%;
    left: -80px;
    animation-delay: 1s;
}

.card-3 {
    bottom: -20%;
    right: 5%;
    animation-delay: 2s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

/* Background Elements */
.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
    animation: floatShape 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 15%;
    animation-delay: 4s;
}

@keyframes floatShape {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Navbar scroll effect */
.navbar-scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
}

/* Animation for navbar items */
.navbar-nav .nav-item {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.navbar-nav .nav-item:nth-child(1) {
    animation-delay: 0.1s;
}

.navbar-nav .nav-item:nth-child(2) {
    animation-delay: 0.2s;
}

.navbar-nav .nav-item:nth-child(3) {
    animation-delay: 0.3s;
}

.navbar-nav .nav-item:nth-child(4) {
    animation-delay: 0.4s;
}

.navbar-nav .nav-item:nth-child(5) {
    animation-delay: 0.5s;
}

.navbar-nav .nav-item:nth-child(6) {
    animation-delay: 0.6s;
}

.navbar-nav .nav-item:nth-child(7) {
    animation-delay: 0.7s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effects for better UX */
.navbar-brand:hover .navbar-logo {
    transform: scale(1.05);
    background: linear-gradient(135deg, #0056b3, var(--primary-color));
}

.navbar-brand:hover .brand-text {
    color: #0056b3;
}

/* Focus states for accessibility */
.nav-link:focus,
.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* About Section */
.about-section {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 86, 179, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.about-image {
    position: relative;
    padding: 2rem 0;
}

.image-wrapper {
    position: relative;
    border-radius: 30px;
    overflow: visible;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    padding: 1.5rem;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.about-main-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(1.05) contrast(1.1);
}

.about-main-image:hover {
    transform: scale(1.02) translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Image Decorations */
.image-decoration {
    position: absolute;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.9),
        inset 0 1px 0 rgba(255, 255, 255, 1);
    animation: modernFloat 4s ease-in-out infinite;
    z-index: 10;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.image-decoration i {
    font-size: 28px;
}

.decoration-1 {
    top: -1rem;
    right: 3rem;
    animation-delay: 0s;
}

.decoration-2 {
    bottom: 25%;
    left: -35px;
    animation-delay: 1.5s;
}

.decoration-3 {
    bottom: -2rem;
    right: 25%;
    animation-delay: 3s;
}

@keyframes modernFloat {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
        box-shadow:
            0 15px 35px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.9);
    }

    25% {
        transform: translateY(-8px) rotate(1deg);
    }

    50% {
        transform: translateY(-12px) rotate(0deg);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.9);
    }

    75% {
        transform: translateY(-8px) rotate(-1deg);
    }
}

/* About Content */
.about-content {
    padding: 2rem 0;
    padding-left: 3rem;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 2rem;
    box-shadow:
        0 10px 25px rgba(0, 123, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.about-title {
    font-size: 2.8rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 2rem;
    color: #1a202c;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

/* Features List */
.about-features {
    margin: 2rem 0;
}

.feature-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    text-align: center;
    margin-bottom: 0;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
}

.feature-item:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 123, 255, 0.2);
    border-color: rgba(0, 123, 255, 0.3);
}

.feature-item:hover .feature-content h6,
.feature-item:hover .feature-content p {
    color: white;
}

.feature-item:hover .feature-icon i {
    color: white !important;
}

.feature-icon {
    margin-right: 0;
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 2rem;
}

.feature-content {
    width: 100%;
}

.feature-content h6 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.75rem;
}

.feature-content p {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin: 0;
    line-height: 1.6;
}

/* About CTA */
.about-cta .btn {
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.about-cta .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border: none;
    box-shadow: 0 15px 35px rgba(0, 123, 255, 0.3);
}

.about-cta .btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 123, 255, 0.4);
    background: linear-gradient(135deg, #0056b3 0%, var(--primary-color) 100%);
}

.about-cta .btn-outline-secondary {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    backdrop-filter: blur(10px);
}

.about-cta .btn-outline-secondary:hover {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-color: #cbd5e0;
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    color: #2d3748;
}

/* Why Choose Section */
.why-choose-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.why-choose-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(0, 123, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(0, 86, 179, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.section-description {
    font-size: 1.1rem;
    color: var(--secondary-color);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Why Cards */
.why-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    position: relative;
}

.why-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(0, 123, 255, 0.15),
        0 0 0 1px rgba(0, 123, 255, 0.1);
}

.card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.why-card:hover .card-image img {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.8) 0%, rgba(0, 86, 179, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.why-card:hover .card-overlay {
    opacity: 1;
}

.card-overlay i {
    font-size: 3rem;
    color: white;
    transform: scale(0.8);
    transition: transform 0.4s ease;
}

.why-card:hover .card-overlay i {
    transform: scale(1);
}

.card-content {
    padding: 1.5rem;
}

.card-content h5 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 0;
    line-height: 1.3;
}

.card-content p {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin: 0;
    line-height: 1.5;
}

/* Custom Grid for 5 columns */
.col-lg-2-4 {
    flex: 0 0 auto;
    width: 20%;
}

/* Features Section */
.features-section {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 80% 20%, rgba(0, 123, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(0, 86, 179, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* Feature Cards */
.feature-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    position: relative;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 123, 255, 0.12),
        0 0 0 1px rgba(0, 123, 255, 0.1);
}

.feature-image {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.feature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-image img {
    transform: scale(1.1);
}

.feature-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.85) 0%, rgba(0, 86, 179, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-overlay {
    opacity: 1;
}

.feature-overlay i {
    font-size: 2rem;
    color: white;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-overlay i {
    transform: scale(1);
}

.feature-content {
    padding: 1rem;
    text-align: center;
}

.feature-content h6 {
    font-size: 0.9rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.feature-content p {
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin: 0;
    line-height: 1.4;
}

/* Partners Section */
.partners-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
    padding: 4rem 0;
}

.partners-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(0, 123, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 86, 179, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* Partner Items */
.partner-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 15px;
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100px;
    position: relative;
}

.partner-item:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 15px 30px rgba(0, 123, 255, 0.1),
        0 0 0 1px rgba(0, 123, 255, 0.1);
}

.partner-logo {
    max-height: 60px;
    max-width: 120px;
    width: auto;
    height: auto;
    object-fit: contain;
    filter: grayscale(100%) opacity(0.7);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.partner-item:hover .partner-logo {
    filter: grayscale(0%) opacity(1);
    transform: scale(1.1);
}

/* Trust Statement */
.trust-statement {
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin: 0;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 86, 179, 0.05) 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.trust-statement strong {
    color: var(--primary-color);
    font-weight: 700;
}

/* Services Section */
.services-section {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 123, 255, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(0, 86, 179, 0.04) 0%, transparent 50%);
    pointer-events: none;
}

/* Service Cards */
.service-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(0, 123, 255, 0.15),
        0 0 0 1px rgba(0, 123, 255, 0.1);
}

.service-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.9) 0%, rgba(0, 86, 179, 0.95) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.service-card:hover .service-overlay {
    opacity: 1;
}

.service-overlay i {
    font-size: 3rem;
    color: white;
    transform: scale(0.8) rotate(-10deg);
    transition: all 0.4s ease;
}

.service-card:hover .service-overlay i {
    transform: scale(1) rotate(0deg);
}

.service-content {
    padding: 2rem;
}

.service-content h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.service-content p {
    font-size: 0.95rem;
    color: var(--secondary-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-tag {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 86, 179, 0.1) 100%);
    color: var(--primary-color);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-tag:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    transform: translateY(-2px);
}

/* Promo Section */
.promo-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.promo-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 25%, rgba(0, 123, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 85% 75%, rgba(0, 86, 179, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* Promo Cards */
.promo-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 25px;
    overflow: hidden;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    position: relative;
    border: 2px solid transparent;
}

.promo-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow:
        0 30px 60px rgba(0, 123, 255, 0.2),
        0 0 0 2px rgba(0, 123, 255, 0.2);
    border-color: rgba(0, 123, 255, 0.3);
}

.promo-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
}

.badge-text {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.promo-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.promo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.promo-card:hover .promo-image img {
    transform: scale(1.1);
}

.promo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.85) 0%, rgba(0, 86, 179, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.promo-card:hover .promo-overlay {
    opacity: 1;
}

.promo-overlay i {
    font-size: 3.5rem;
    color: white;
    transform: scale(0.8) rotate(-15deg);
    transition: all 0.4s ease;
}

.promo-card:hover .promo-overlay i {
    transform: scale(1) rotate(0deg);
}

.promo-content {
    padding: 2rem;
}

.promo-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.promo-description {
    font-size: 0.95rem;
    color: var(--secondary-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.promo-features {
    margin-bottom: 1rem;
}

.promo-features .feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.3rem;
    transition: all 0.3s ease;
}

.promo-features .feature-item i {
    margin-right: 0.4rem;
    font-size: 0.8rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.promo-features .feature-item span {
    font-size: 0.85rem;
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

.promo-features .feature-item:hover span,
.promo-features .feature-item:hover i {
    color: white !important;
}

.promo-price {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.original-price {
    font-size: 0.9rem;
    color: var(--secondary-color);
    text-decoration: line-through;
}

.free-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.btn-promo {
    width: 100%;
    padding: 0.8rem 1.5rem;
    font-weight: 600;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.btn-promo:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
}

/* Promo CTA */
.promo-cta {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 86, 179, 0.05) 100%);
    padding: 3rem 2rem;
    border-radius: 25px;
    border: 2px solid rgba(0, 123, 255, 0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.promo-cta::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(0, 123, 255, 0.03), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

.promo-cta h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.promo-cta p {
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.promo-cta .btn {
    position: relative;
    z-index: 2;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.promo-cta .btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

/* Testimonials Section */
.testimonials-section {
    background: linear-gradient(135deg, #f1f5f9 0%, #ffffff 50%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
    padding: 5rem 0;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(0, 123, 255, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(0, 86, 179, 0.04) 0%, transparent 50%);
    pointer-events: none;
}

/* Testimonial Cards */
.testimonial-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 25px;
    padding: 3rem 2.5rem;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.9);
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow:
        0 30px 60px rgba(0, 123, 255, 0.12),
        0 0 0 1px rgba(0, 123, 255, 0.1);
}

.testimonial-content {
    position: relative;
    z-index: 1;
    padding-top: 2rem;
}

.quote-icon {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
    z-index: 10;
}

.quote-icon i {
    font-size: 1.5rem;
    color: white;
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--secondary-color);
    margin: 2rem 0;
    font-style: italic;
    position: relative;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
}

.author-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(0, 123, 255, 0.1);
    transition: all 0.3s ease;
}

.testimonial-card:hover .author-image {
    border-color: rgba(0, 123, 255, 0.3);
    transform: scale(1.05);
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info h5 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 0.3rem;
}

.author-info span {
    font-size: 0.9rem;
    color: var(--secondary-color);
    display: block;
    margin-bottom: 0.5rem;
}

.rating {
    display: flex;
    gap: 0.2rem;
    justify-content: center;
}

.rating i {
    color: #ffc107;
    font-size: 0.9rem;
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
    width: 60px;
    height: 60px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
}

.carousel-control-prev {
    left: -80px;
}

.carousel-control-next {
    right: -80px;
}

.carousel-control-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
}

.carousel-control-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 35px rgba(0, 123, 255, 0.4);
}

.carousel-control-icon i {
    font-size: 1.2rem;
    color: white;
}



/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
    padding: 6rem 0;
}

.cta-content {
    position: relative;
    z-index: 10;
}



/* CTA Inner Content */
.cta-inner {
    position: relative;
    z-index: 5;
}

.cta-text {
    color: white;
}

.cta-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.cta-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.95;
}

.cta-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.cta-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
}

.cta-feature i {
    color: #28a745;
    font-size: 1.1rem;
}

.cta-feature span {
    font-size: 1rem;
    font-weight: 500;
}

/* CTA Actions */
.cta-actions {
    text-align: center;
}

.cta-btn-primary,
.cta-btn-secondary {
    width: 100%;
    margin-bottom: 1rem;
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: 15px;
    transition: all 0.3s ease;
    border-width: 2px;
}

.cta-btn-primary {
    background: white;
    color: var(--primary-color);
    border-color: white;
}

.cta-btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.cta-btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.cta-btn-secondary:hover {
    background: white;
    color: var(--primary-color);
    border-color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.cta-contact {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: white;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.contact-item i {
    opacity: 0.8;
}

/* Footer Section */
.footer-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    padding: 4rem 0 0;
    position: relative;
}

.footer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, #0056b3 100%);
}

/* Footer Widgets */
.footer-widget {
    margin-bottom: 2rem;
}

.footer-logo h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-description {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.footer-title {
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-color);
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #cccccc;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

/* Social Links */
.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #cccccc;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* Footer Contact */
.footer-contact .contact-item {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    color: #cccccc;
}

.footer-contact .contact-item i {
    color: var(--primary-color);
    font-size: 1.1rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.contact-info span {
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-copyright p {
    margin: 0;
    color: #cccccc;
    font-size: 0.9rem;
}

.footer-legal {
    display: flex;
    gap: 2rem;
    justify-content: flex-end;
}

.footer-legal a {
    color: #cccccc;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-legal a:hover {
    color: var(--primary-color);
}

/* Contact Steps Section */
.contact-steps-section {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.steps-timeline {
    position: relative;
    padding: 2rem 0;
}

.steps-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, var(--primary-color) 0%, #0056b3 100%);
}

.step-item {
    position: relative;
    padding-left: 100px;
    margin-bottom: 4rem;
}

.step-item:last-child {
    margin-bottom: 0;
}

.step-number {
    position: absolute;
    left: 0;
    top: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 20px rgba(0, 123, 255, 0.3);
    z-index: 2;
}

.step-number span {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.step-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.step-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.15);
}

.step-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 86, 179, 0.1) 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.step-icon i {
    font-size: 2rem;
    color: var(--primary-color);
}

.step-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 1rem;
}

.step-description {
    color: #666;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.step-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.step-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #444;
}

.step-features li i {
    color: #28a745;
    font-size: 1.1rem;
}

.step-action {
    margin-top: 1.5rem;
}

.step-action .btn {
    padding: 0.75rem 2rem;
    border-radius: 10px;
}

/* Contact CTA */
.contact-cta {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    padding: 3rem 2rem;
    border-radius: 20px;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
}

.contact-cta h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.contact-cta p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.95;
}

.contact-cta .btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
    padding: 1rem 2.5rem;
    border-radius: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.contact-cta .btn:hover {
    background: transparent;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

/* Registration Section */
.registration-section {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.registration-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1a1a1a;
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.registration-subtitle {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.8;
    margin-bottom: 2rem;
}

/* Advantages Grid */
.advantages-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.advantage-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
}

.advantage-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.15);
}

.advantage-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.advantage-icon i {
    font-size: 2rem;
    color: white;
}

.advantage-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 1rem;
}

.advantage-description {
    color: #666;
    line-height: 1.7;
    margin: 0;
}

/* Registration Form */
.registration-form-wrapper {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.form-header {
    text-align: center;
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #e9ecef;
}

.form-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
}

.form-subtitle {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

.registration-form .form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.registration-form .form-control {
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.registration-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.1);
}

.registration-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.registration-form .form-check {
    padding: 0.5rem 0;
    margin-bottom: 0.5rem;
    margin-left: 1.5rem;
}

.registration-form .form-check-input {
    width: 1.2rem;
    height: 1.2rem;
    margin-top: 0.2rem;
    border: 2px solid #dee2e6;
    cursor: pointer;
}

.registration-form .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.registration-form .form-check-label {
    margin-left: 0.5rem;
    cursor: pointer;
    color: #444;
}

.submit-btn {
    padding: 1rem 3rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 15px;
    transition: all 0.3s ease;
    min-width: 250px;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

/* Success Section */
.success-section {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.success-wrapper {
    background: white;
    padding: 4rem 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
}

/* Success Icon */
.success-icon-wrapper {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto 2rem;
}

.success-icon {
    position: relative;
    z-index: 2;
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
}

.success-icon i {
    font-size: 5rem;
    color: white;
}

/* Success Animation */
.success-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.success-animation div {
    position: absolute;
    border-radius: 50%;
    border: 2px solid #28a745;
    opacity: 0;
    animation: ripple 2s ease-out infinite;
}

.circle-1 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 0.7s;
}

.circle-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 1.4s;
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }

    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* Success Content */
.success-content {
    margin-bottom: 3rem;
}

.success-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #28a745;
    margin-bottom: 1rem;
}

.success-message {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.8;
    margin: 0;
}

/* Next Steps */
.next-steps {
    margin-bottom: 3rem;
    text-align: left;
}

.next-steps-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 2rem;
    text-align: center;
}

.steps-list {
    display: grid;
    gap: 1.5rem;
}

.step-item-success {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.step-item-success:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.step-icon-success {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.step-icon-success i {
    font-size: 1.5rem;
    color: white;
}

.step-text h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
}

.step-text p {
    font-size: 0.95rem;
    color: #666;
    margin: 0;
    line-height: 1.6;
}

/* Success Actions */
.success-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.success-actions .btn {
    padding: 1rem 2rem;
    border-radius: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.success-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

/* Success Contact */
.success-contact {
    padding: 1.5rem;
    background: #fff3cd;
    border-radius: 10px;
    border-left: 4px solid #ffc107;
}

.contact-text {
    margin: 0;
    color: #856404;
    font-size: 0.95rem;
    line-height: 1.6;
}

.contact-text i {
    color: #ffc107;
}

.contact-text a {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
}

.contact-text a:hover {
    text-decoration: underline;
}