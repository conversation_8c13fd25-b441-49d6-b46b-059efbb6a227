# Smart School Project Landing Page

Landing page untuk Smart School Project yang dibangun dengan Laravel 12. Platform digitalisasi sekolah terbaik di Indonesia.

## 🚀 Fitur

- **Landing Page Responsif**: Desain modern dan mobile-friendly
- **Form Pendaftaran**: Sistem pendaftaran dengan validasi lengkap
- **Email Notification**: Notifikasi otomatis ke tim sales
- **Database Integration**: Penyimpanan data pendaftaran
- **SEO Optimized**: Meta tags, sitemap, dan robots.txt
- **Security Headers**: Keamanan tingkat enterprise
- **Caching**: Optimasi performa dengan caching

## 📋 Persyaratan Sistem

- PHP >= 8.2
- Composer
- MySQL/MariaDB
- Node.js & NPM (untuk asset compilation)

## 🛠️ Instalasi

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd ssplanding_system
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Konfigurasi database**
   Edit file `.env` dan sesuaikan konfigurasi database:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=ssplanding_system
   DB_USERNAME=root
   DB_PASSWORD=
   ```

5. **Jalankan migration**
   ```bash
   php artisan migrate
   ```

6. **Compile assets**
   ```bash
   npm run build
   ```

7. **Jalankan server**
   ```bash
   php artisan serve
   ```

## 📧 Konfigurasi Email

Untuk mengaktifkan notifikasi email, konfigurasi SMTP di file `.env`:

```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Smart School Project"
```

## 🗂️ Struktur Project

```
ssplanding_system/
├── app/
│   ├── Http/Controllers/
│   │   ├── HomeController.php
│   │   ├── RegistrationController.php
│   │   └── ContactController.php
│   ├── Mail/
│   │   └── RegistrationNotification.php
│   ├── Models/
│   │   └── Registration.php
│   └── Http/Middleware/
│       └── CacheHeaders.php
├── resources/
│   └── views/
│       ├── layouts/
│       │   └── app.blade.php
│       ├── emails/
│       │   └── registration-notification.blade.php
│       ├── home.blade.php
│       ├── register.blade.php
│       ├── contact.blade.php
│       └── registration-success.blade.php
├── public/
│   ├── css/
│   ├── js/
│   ├── style.css
│   └── script.js
└── routes/
    └── web.php
```

## 🎯 Halaman Utama

- **/** - Landing page utama
- **/daftar** - Form pendaftaran
- **/hubungi-kami** - Informasi kontak dan tahapan kerja sama
- **/pendaftaran-berhasil** - Halaman sukses pendaftaran

## 🔧 Optimasi Production

Untuk deployment production, jalankan:

```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev

# Build assets for production
npm run build
```

## 📊 Database Schema

### Tabel `registrations`
- `id` - Primary key
- `nama` - Nama lengkap pendaftar
- `jabatan` - Jabatan di institusi
- `email_perorangan` - Email pribadi
- `email_sekolah` - Email institusi
- `whatsapp` - Nomor WhatsApp
- `institusi` - Nama institusi
- `pesan_utama` - Layanan yang diminati
- `pesan_tambahan` - Pesan tambahan (optional)
- `created_at` - Waktu pendaftaran
- `updated_at` - Waktu update terakhir

## 🛡️ Keamanan

- CSRF Protection pada semua form
- Input validation dan sanitization
- Security headers (XSS, CSRF, Content-Type)
- SQL injection protection melalui Eloquent ORM

## 📱 Responsive Design

Landing page telah dioptimasi untuk:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## 🤝 Kontribusi

1. Fork repository
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 License

Project ini menggunakan [MIT License](LICENSE).

## 📞 Support

Untuk pertanyaan atau dukungan teknis, hubungi:
- Email: <EMAIL>
- Website: https://smartschoolproject.id
