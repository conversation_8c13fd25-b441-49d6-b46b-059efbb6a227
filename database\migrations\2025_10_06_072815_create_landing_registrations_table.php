<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('landing_registrations', function (Blueprint $table) {
            $table->id();
            $table->string('nama');
            $table->string('jabatan');
            $table->string('email_perorangan');
            $table->string('email_sekolah');
            $table->string('whatsapp', 20);
            $table->string('institusi');
            $table->text('pesan_utama'); // Stored as comma-separated values
            $table->text('pesan_tambahan')->nullable();
            $table->string('source')->default('landing_page'); // To identify source
            $table->enum('status', ['pending', 'contacted', 'converted', 'rejected'])->default('pending');
            $table->text('notes')->nullable(); // For admin notes
            $table->timestamps();

            // Add indexes for better performance
            $table->index('email_perorangan');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('landing_registrations');
    }
};
