@extends('layouts.app')

@section('title', 'Pendaftaran Berhasil - Smart School Project')

@section('description',
    'Terima kasih telah mendaftar di Smart School Project. <PERSON> kami akan segera menghubungi anda
    dengan memberikan akses login ke panel.')

@section('content')
    <!-- Success Section -->
    <section id="success" class="success-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="success-wrapper">
                        <!-- Success Icon -->
                        <div class="success-icon-wrapper">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="success-animation">
                                <div class="circle-1"></div>
                                <div class="circle-2"></div>
                                <div class="circle-3"></div>
                            </div>
                        </div>

                        <!-- Success Message -->
                        <div class="success-content">
                            <h1 class="success-title">Pendaftaran Berhasil!</h1>
                            <p class="success-message">
                                <PERSON> kami akan segera menghubungi anda dengan memberikan akses login ke panel
                            </p>
                        </div>

                        <!-- Next Steps -->
                        <div class="next-steps">
                            <h3 class="next-steps-title">Apa Selanjutnya?</h3>
                            <div class="steps-list">
                                <div class="step-item-success">
                                    <div class="step-icon-success">
                                        <i class="fas fa-envelope-open-text"></i>
                                    </div>
                                    <div class="step-text">
                                        <h4>Cek Email Anda</h4>
                                        <p>Kami akan mengirimkan email konfirmasi ke alamat yang Anda daftarkan</p>
                                    </div>
                                </div>

                                <div class="step-item-success">
                                    <div class="step-icon-success">
                                        <i class="fas fa-phone-alt"></i>
                                    </div>
                                    <div class="step-text">
                                        <h4>Tunggu Kontak dari Tim Kami</h4>
                                        <p>Tim kami akan menghubungi Anda melalui WhatsApp atau email dalam 1x24 jam</p>
                                    </div>
                                </div>

                                <div class="step-item-success">
                                    <div class="step-icon-success">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div class="step-text">
                                        <h4>Dapatkan Akses Login</h4>
                                        <p>Anda akan menerima kredensial login untuk mengakses panel Smart School
                                            Project</p>
                                    </div>
                                </div>

                                <div class="step-item-success">
                                    <div class="step-icon-success">
                                        <i class="fas fa-rocket"></i>
                                    </div>
                                    <div class="step-text">
                                        <h4>Mulai Transformasi Digital</h4>
                                        <p>Nikmati semua fitur Smart School Project dan mulai digitalisasi sekolah Anda
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="success-actions">
                            <a href="{{ route('home') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-home me-2"></i>
                                Kembali ke Beranda
                            </a>
                            <a href="{{ route('contact') }}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-phone me-2"></i>
                                Hubungi Kami
                            </a>
                        </div>

                        <!-- Contact Info -->
                        <div class="success-contact">
                            <p class="contact-text">
                                <i class="fas fa-info-circle me-2"></i>
                                Jika Anda tidak menerima email dalam 24 jam, silakan hubungi kami di
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('additional_css')
    <style>
        /* Root Variables */
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --accent-color: #ff6b35;
            --light-gray: #f8f9fa;
            --dark-gray: #343a40;
            --white: #ffffff;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* Success Section */
        .success-section {
            padding: 8rem 0 4rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .success-wrapper {
            background: white;
            padding: 4rem 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        /* Success Icon */
        .success-icon-wrapper {
            position: relative;
            width: 150px;
            height: 150px;
            margin: 0 auto 2rem;
        }

        .success-icon {
            position: relative;
            z-index: 2;
            width: 150px;
            height: 150px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        }

        .success-icon i {
            font-size: 5rem;
            color: white;
        }

        /* Success Animation */
        .success-animation {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
        }

        .success-animation div {
            position: absolute;
            border-radius: 50%;
            border: 2px solid #28a745;
            opacity: 0;
            animation: ripple 2s ease-out infinite;
        }

        .circle-1 {
            width: 150px;
            height: 150px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 0s;
        }

        .circle-2 {
            width: 150px;
            height: 150px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 0.7s;
        }

        .circle-3 {
            width: 150px;
            height: 150px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: 1.4s;
        }

        @keyframes ripple {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.6;
            }

            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }

        /* Success Content */
        .success-content {
            margin-bottom: 3rem;
        }

        .success-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: #28a745;
            margin-bottom: 1rem;
        }

        .success-message {
            font-size: 1.2rem;
            color: #666;
            line-height: 1.8;
            margin: 0;
        }

        /* Next Steps */
        .next-steps {
            margin-bottom: 3rem;
            text-align: left;
        }

        .next-steps-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 2rem;
            text-align: center;
        }

        .steps-list {
            display: grid;
            gap: 1.5rem;
        }

        .step-item-success {
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .step-item-success:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .step-icon-success {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .step-icon-success i {
            font-size: 1.5rem;
            color: white;
        }

        .step-text h4 {
            font-size: 1.1rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 0.5rem;
        }

        .step-text p {
            font-size: 0.95rem;
            color: #666;
            margin: 0;
            line-height: 1.6;
        }

        /* Success Actions */
        .success-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .success-actions .btn {
            padding: 1rem 2rem;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .success-actions .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
        }

        /* Success Contact */
        .success-contact {
            padding: 1.5rem;
            background: #fff3cd;
            border-radius: 10px;
            border-left: 4px solid #ffc107;
        }

        .contact-text {
            margin: 0;
            color: #856404;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .contact-text i {
            color: #ffc107;
        }

        .contact-text a {
            color: var(--primary-color);
            font-weight: 600;
            text-decoration: none;
        }

        .contact-text a:hover {
            text-decoration: underline;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .success-section {
                padding: 6rem 0 3rem;
            }

            .success-wrapper {
                padding: 2.5rem 1.5rem;
            }

            .success-icon-wrapper {
                width: 120px;
                height: 120px;
            }

            .success-icon {
                width: 120px;
                height: 120px;
            }

            .success-icon i {
                font-size: 4rem;
            }

            .circle-1,
            .circle-2,
            .circle-3 {
                width: 120px;
                height: 120px;
            }

            .success-title {
                font-size: 2rem;
            }

            .success-message {
                font-size: 1rem;
            }

            .next-steps-title {
                font-size: 1.3rem;
            }

            .step-item-success {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .step-icon-success {
                margin: 0 auto;
            }

            .success-actions {
                flex-direction: column;
            }

            .success-actions .btn {
                width: 100%;
            }

            .contact-text {
                font-size: 0.85rem;
            }
        }
    </style>
@endsection
